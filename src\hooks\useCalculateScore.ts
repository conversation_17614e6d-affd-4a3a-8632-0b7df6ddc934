import { useMutation } from '@tanstack/react-query'
import axios from 'axios'

interface CalculateScoreRequest {
  userId: string
  gameCampaignId: string
  levelNumber: number
  timeCompleted: number
}

export interface CalculateScoreResponse {
  image: string
  rewardKey: number
  segment: number
  score: number
  title: {
    en: string
    id: string
    ko: string
    th: string
    vi: string
  }
}

export function useCalculateScore() {
  return useMutation<CalculateScoreResponse, Error, CalculateScoreRequest>({
    mutationFn: async (payload: CalculateScoreRequest) => {
      const { data } = await axios.post('/api/calculate-score', payload, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
      return data
    },
    onError: (error) => {
      console.error('Calculate score mutation error:', error)
    },
    onSuccess: (data) => {
      console.log('Calculate score successful:', data)
    }
  })
}
