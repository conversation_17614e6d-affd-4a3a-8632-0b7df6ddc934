// src/hooks/useForfeitSpin.ts
import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { ForfeitSpinRequest, ForfeitSpinResponse } from '@/app/api/forfeitSpin/route'

export function useForfeitSpin() {
  return useMutation<ForfeitSpinResponse, Error, ForfeitSpinRequest>({
    mutationFn: async (payload: ForfeitSpinRequest) => {
      const { data } = await axios.post('/api/forfeitSpin', payload, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
      return data
    },
    onError: (error) => {
      console.error('Forfeit spin mutation error:', error)
    }
  })
}
