// src/app/api/get-history-game-campaign/route.ts
import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'

interface GetHistoryGameCampaignRequest {
  userId: string
  gameCampaignId: string
}

export interface HistoryEntry {
  createdAt: string
  icon?: string
  image?: string
  level?: number
  navigateTo?: string
  title: {
    en: string
    id: string
    ko: string
    ms: string
    th: string
    vi: string
  }
}

export type GetHistoryGameCampaignResponse = HistoryEntry[]

export async function POST(req: NextRequest) {
  try {
    const body: GetHistoryGameCampaignRequest = await req.json()
    console.log('Get History Game Campaign API - Incoming Request:', body)

    // Validate required fields
    if (!body.userId || !body.gameCampaignId) {
      return NextResponse.json({ error: 'Missing required fields: userId, gameCampaignId' }, { status: 400 })
    }

    const BE_URL = process.env.BE_URL
    const ACCESS_KEY_BE = process.env.ACCESS_KEY_BE

    if (!BE_URL || !ACCESS_KEY_BE) {
      return NextResponse.json({ error: 'Server misconfiguration: missing BE_URL or ACCESS_KEY_BE' }, { status: 500 })
    }

    const { data } = await axios.post<GetHistoryGameCampaignResponse>(
      `${BE_URL}/api-asker-vn/get-history-game-campaign`,
      body,
      {
        headers: {
          accessKey: ACCESS_KEY_BE,
          Authorization:
            'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTE0MTM4OH0.U19XYfTHpIO6vU_m8gYKGV4DAT8WVd9c2H5gG4D5Y6U',
          'Content-Type': 'application/json'
        }
      }
    )

    console.log('Get History Game Campaign API - Outgoing Response Data:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Error while handling get history game campaign request:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json({ error: err.message }, { status: 500 })
  }
}
