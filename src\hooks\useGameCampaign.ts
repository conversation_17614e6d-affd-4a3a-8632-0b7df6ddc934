import { useMutation } from '@tanstack/react-query'
import axios from 'axios'

interface GameCampaignRequest {
  userId: string
  appVersion: string
  from: string
}

// src/hooks/useGameCampaign.ts
export interface GameCampaignResponse {
  _id: string
  avatar: string
  checkListMission: {
    canSharePostToday: boolean
    isCheckInToday: boolean
    isFriendDoneTaskFirst: boolean
    isJoinFirstGame: boolean
  }
  furthestLevel: number
  infoLevels: {
    level: number
    score: number
    rewardInfo: {
      icon: string
      title: Record<string, string>
    }
  }[]
  isPlayedGame: boolean
  numberOfTurn: number
  numberOfGift: number
  username: string
}

export const useGameCampaign = () => {
  return useMutation({
    mutationFn: async (payload: GameCampaignRequest) => {
      const { data } = await axios.post('/api/gameCampaign', payload)
      return data
    }
  })
}
