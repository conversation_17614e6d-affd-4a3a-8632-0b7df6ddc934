// src/app/api/forfeitSpin/route.ts
import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'

export interface ForfeitSpinRequest {
  action: 'FORFEIT_SPIN' | 'RETRY'
  levelNumber: number
  gameCampaignId: string
  userId: string
}

export interface ForfeitSpinResponse {
  success: boolean
  message?: string
  // Add other response fields as needed based on actual API response
}

export async function POST(req: NextRequest) {
  try {
    const body: ForfeitSpinRequest = await req.json()
    console.log('Incoming Request Body:', body)

    const baseUrl = process.env.BE_URL
    const accessKey = process.env.ACCESS_KEY_BE

    if (!baseUrl || !accessKey) {
      throw new Error('Missing BE_URL or ACCESS_KEY_BE in environment variables')
    }

    const { data } = await axios.post<ForfeitSpinResponse>(`${baseUrl}/event-vn/forfeit-spin`, body, {
      headers: {
        accessKey,
        Authorization:
          'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTE0MTM4OH0.U19XYfTHpIO6vU_m8gYKGV4DAT8WVd9c2H5gG4D5Y6U',
        'Content-Type': 'application/json'
      }
    })

    console.log('Outgoing Response Data:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Forfeit spin API error:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json(
      { error: err.message || 'Failed to process forfeit spin request' },
      { status: err.response?.status ?? 500 }
    )
  }
}
