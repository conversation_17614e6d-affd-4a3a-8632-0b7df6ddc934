import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface UsePresentUnboxingRequest {
  gameCampaignId: string
  from: string
  levelNumber: number
  timeCompleted: number
  userId: string
}

interface UsePresentUnboxingResponse {
  image: string
  rewardKey: number
  segment: number
  score: number
  title: {
    en: string
    id: string
    ko: string
    th: string
    vi: string
  }
}

export async function POST(req: NextRequest) {
  try {
    const body: UsePresentUnboxingRequest = await req.json()
    console.log('Use Present Unboxing API - Incoming Request:', body)

    // Validate required fields
    if (
      !body.gameCampaignId ||
      !body.from ||
      typeof body.levelNumber !== 'number' ||
      typeof body.timeCompleted !== 'number' ||
      !body.userId
    ) {
      return NextResponse.json(
        { error: 'Missing required fields: gameCampaignId, from, levelNumber, timeCompleted, userId' },
        { status: 400 }
      )
    }

    // Update user level tracking
    UserLevelManager.updateUserLevel(body.userId, body.levelNumber, true)
    UserLevelManager.updateUserLevel(body.userId, body.levelNumber + 1, false)

    console.log('Updated user level tracking for userId:', body.userId, 'level:', body.levelNumber)

    // External API request payload
    const externalApiPayload = {
      gameCampaignId: body.gameCampaignId,
      from: body.from,
      levelNumber: body.levelNumber,
      timeCompleted: body.timeCompleted,
      userId: body.userId
    }

    // Read from env
    const BE_URL = process.env.BE_URL
    const ACCESS_KEY_BE = process.env.ACCESS_KEY_BE

    if (!BE_URL || !ACCESS_KEY_BE) {
      throw new Error('Missing BE_URL or ACCESS_KEY_BE in environment variables')
    }

    // Call the external BE API
    const { data } = await axios.post<UsePresentUnboxingResponse>(
      `${BE_URL}/event-vn/use-present-unboxing`,
      externalApiPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          accessKey: ACCESS_KEY_BE,
          Authorization:
            'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTE0MTM4OH0.U19XYfTHpIO6vU_m8gYKGV4DAT8WVd9c2H5gG4D5Y6U'
        }
      }
    )

    console.log('Use Present Unboxing API - External API Response:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Use Present Unboxing API error:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json(
      { error: err.message || 'Failed to process unboxing request' },
      { status: err.response?.status ?? 500 }
    )
  }
}
