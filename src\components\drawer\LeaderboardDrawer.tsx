'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON>lose, DrawerFooter } from '@/components/ui/drawer'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import MediumerOrangeButton from '@/assets/drawer/mediumerOrangeButton.svg'
import MediumGreenButton from '@/assets/drawer/mediumGreenButton.svg'
import EmptyLeaderboard from '@/assets/drawer/empty_leaderboard.svg'
import Image from 'next/image'
import { useRankingGameCampaign, type RankingResponse } from '@/hooks/useRankingGameCampaign'
import { useEffect } from 'react'
import { useShareDialogStore } from '@/stores/useShareDialogStore'

interface LeaderboardDrawerProps {
  open: boolean
  onClose: () => void
}

const defaultLeaderboard: RankingResponse[] = [
  // {
  //   avatar: 'https://i1.sndcdn.com/avatars-000191448961-vwi4k6-t240x240.jpg',
  //   createdAt: '',
  //   isTarget: false,
  //   name: 'Linda',
  //   rank: 1,
  //   score: { maxLevel: 0, total: 8456 }
  // },
  // {
  //   avatar: 'https://cf.shopee.vn/file/f66a6c09eec5298af5ca3f09f87ed279',
  //   createdAt: '',
  //   isTarget: false,
  //   name: 'Trần My',
  //   rank: 2,
  //   score: { maxLevel: 0, total: 8456 }
  // },
  // {
  //   avatar:
  //     'https://danviet-24h.ex-cdn.com/files/upload/2-2020/images/2020-05-12/83395730_134841691333804_1955901610945150976_o-1589260776-4-width960height960.jpg',
  //   createdAt: '',
  //   isTarget: false,
  //   name: 'Minh Hiếu',
  //   rank: 3,
  //   score: { maxLevel: 0, total: 8456 }
  // },
  // {
  //   avatar: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT2HzojwlBpo-h3vaRPFSLRYNkKoSiKUa-B1Q&s',
  //   createdAt: '',
  //   isTarget: false,
  //   name: 'Phan Kim Tiền',
  //   rank: 4,
  //   score: { maxLevel: 0, total: 3111 }
  // },
  // {
  //   avatar: 'https://nld.mediacdn.vn/291774122806476800/2024/9/19/1000004064-17267122323471192022345.jpg',
  //   createdAt: '',
  //   isTarget: false,
  //   name: 'Nguyễn Phương Hằng',
  //   rank: 5,
  //   score: { maxLevel: 0, total: 5643 }
  // },
  // {
  //   avatar: 'https://cdn.24h.com.vn/upload/4-2021/images/2021-10-27/viethuong6-1635308276-83-width768height595.jpg',
  //   createdAt: '',
  //   isTarget: false,
  //   name: 'Parrot Smell',
  //   rank: 6,
  //   score: { maxLevel: 0, total: 5643 }
  // },
  // {
  //   avatar:
  //     'https://i.vgt.vn/2024/1/14/co-hai-bao-khien-nhieu-nguoi-quay-xe-tu-tong-tai-hoa-thu-ky-kim-xinh-dep-321-7074278.png',
  //   createdAt: '',
  //   isTarget: false,
  //   name: 'Cô Hai Báo',
  //   rank: 7749,
  //   score: { maxLevel: 0, total: 69 }
  // }
]

const RankRow: React.FC<{ entry: RankingResponse }> = ({ entry }) => (
  <div className='relative flex h-[10.29dvh] w-full flex-col justify-end'>
    <div className='absolute left-[10%] top-0 z-10 aspect-square h-[75%] rounded-full'>
      <AspectRatio ratio={1}>
        <Avatar className='h-full w-full'>
          <AvatarImage src={entry.avatar} alt={entry.name} />
          <AvatarFallback>{(entry.name || 'U').charAt(0)}</AvatarFallback>
        </Avatar>
      </AspectRatio>
      <div className='absolute -bottom-[15%] left-1/2 z-10 flex h-5 w-5 -translate-x-1/2 items-center justify-center rounded-full bg-[#F7D6A1]'>
        <span className='translate-y-0.25 translate-x-[0.4px] text-[16px] font-bold text-black'>{entry.rank}</span>
      </div>
    </div>

    <div className='backdrop-blur-xs flex h-[85%] w-full flex-col justify-center rounded-3xl border border-white/30 bg-white/5 pl-[30%] text-white'>
      <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-bold text-transparent'>
        {entry.name}
      </span>
      <span className='text-[14px] font-medium'>{entry.score.total.toLocaleString()} điểm</span>
    </div>
  </div>
)

const LeaderboardDrawer: React.FC<LeaderboardDrawerProps> = ({ open, onClose }) => {
  const userId = 'x169079fd73a4533d4fd5e79097122a9b' // supply from auth/session later
  const { data, refetch, isFetching } = useRankingGameCampaign(userId)
  const openShare = useShareDialogStore((s) => s.open)

  useEffect(() => {
    if (open) {
      refetch()
    }
  }, [open, refetch])

  // rank 1 from API if available
  const top = data?.[0] ?? defaultLeaderboard[0]
  const second = data?.[1] ?? defaultLeaderboard[1]
  const third = data?.[2] ?? defaultLeaderboard[2]
  const lowest =
    data && data.length > 0
      ? data.reduce((prev, curr) => (curr.rank > prev.rank ? curr : prev))
      : defaultLeaderboard.length > 0
        ? defaultLeaderboard.reduce((prev, curr) => (curr.rank > prev.rank ? curr : prev))
        : null

  const hasData = (data && data.length > 0) || defaultLeaderboard.length > 0

  return (
    <Drawer open={open} onOpenChange={onClose}>
      <DrawerContent className='flex h-[97.5dvh] flex-col overflow-hidden'>
        {/* Fixed header */}
        <DrawerHeader className='h-[18.5%]'>
          <DrawerTitle className='font-bdstreet text-border-orange text-[32px]'>Bảng xếp hạng</DrawerTitle>
        </DrawerHeader>

        <div className='font-montserrat flex h-[70.5%] flex-col overflow-hidden'>
          {!hasData ? (
            <div className='flex flex-1 flex-col items-center justify-center'>
              <div className='flex w-[50%] flex-col items-center space-y-[20%]'>
                <AspectRatio ratio={1}>
                  <EmptyLeaderboard />
                </AspectRatio>
                <p className='text-center text-[14px] font-medium text-white'>Danh sách trống</p>
              </div>
            </div>
          ) : (
            <>
              {/* Top div - 43% height */}
              <div className='flex h-[43%] flex-col'>
                <div className='flex h-full flex-row justify-between'>
                  {/* Column 1 - rank 2 */}
                  <div className='relative flex h-full w-1/3 items-end justify-end pr-[2.5%]'>
                    {second ? (
                      <div className='relative aspect-[78/133] h-[60%]'>
                        <div className='z-0 mx-auto aspect-square w-[90%] rounded-full bg-gradient-to-b from-[#FFFFFF] to-[#C9C9C9] p-[3%]'>
                          <AspectRatio ratio={1}>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={second.avatar} alt={second.name} />
                              <AvatarFallback>{(second.name || 'U').charAt(0)}</AvatarFallback>
                            </Avatar>
                          </AspectRatio>
                        </div>
                        <div className='absolute left-1/2 top-[52.5%] z-10 flex h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-[#FFFFFF]'>
                          <span className='translate-y-0.25 translate-x-[0.4px] text-[16px] font-bold text-black'>
                            2
                          </span>
                        </div>
                        <div className='absolute bottom-[2%] left-1/2 flex w-full -translate-x-1/2 flex-col items-center'>
                          <span className='text-[16px] font-bold text-[#FFFFFF]'>{second.name}</span>
                          <span className='text-[14px] font-medium text-white'>
                            {second.score.total.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    ) : null}
                  </div>

                  {/* Column 2 - rank 1 */}
                  <div className='relative flex h-full w-1/3 flex-col items-center justify-center'>
                    {top ? (
                      <div className='relative flex aspect-[104/203] h-[90%] items-center justify-center'>
                        <div className='absolute left-1/2 top-[5%] z-10 w-[55%] -translate-x-1/2'>
                          <AspectRatio ratio={221 / 207}>
                            <Image src='/drawer/crown.png' alt='Crown' fill quality={100} />
                          </AspectRatio>
                        </div>
                        <div className='z-0 aspect-square w-full rounded-full bg-gradient-to-b from-[#FFFF70] to-[#F6B936] p-[3%]'>
                          <AspectRatio ratio={1}>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={top.avatar} alt={top.name} />
                              <AvatarFallback>{(top.name || 'U').charAt(0)}</AvatarFallback>
                            </Avatar>
                          </AspectRatio>
                        </div>
                        <div className='absolute bottom-[13.5%] left-1/2 z-10 flex h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-[#FFFF70]'>
                          <span className='translate-y-0.25 translate-x-[0.4px] text-[16px] font-bold text-black'>
                            1
                          </span>
                        </div>
                        <div className='absolute -bottom-[5%] flex w-[200%] w-full flex-col items-center'>
                          <span className='line-clamp-1 bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-center text-[16px] font-bold text-transparent'>
                            {top.name}
                          </span>
                          <span className='text-[14px] font-medium text-white'>{top.score.total.toLocaleString()}</span>
                        </div>
                      </div>
                    ) : null}
                  </div>

                  {/* Column 3 - rank 3 */}
                  <div className='relative flex h-full w-1/3 items-end justify-start pl-[2.5%]'>
                    {third ? (
                      <div className='relative aspect-[78/133] h-[60%]'>
                        <div className='z-0 mx-auto aspect-square w-[90%] rounded-full bg-gradient-to-b from-[#E0AE78] to-[#CC863A] p-[3%]'>
                          <AspectRatio ratio={1}>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={third.avatar} alt={third.name} />
                              <AvatarFallback>{(third.name || 'U').charAt(0)}</AvatarFallback>
                            </Avatar>
                          </AspectRatio>
                        </div>
                        <div className='absolute left-1/2 top-[52.5%] z-10 flex h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-[#F6B936]'>
                          <span className='translate-y-0.25 translate-x-[0.4px] text-[16px] font-bold text-black'>
                            3
                          </span>
                        </div>
                        <div className='absolute bottom-[2%] left-1/2 flex w-[150%] -translate-x-1/2 flex-col items-center'>
                          <span className='line-clamp-1 text-[16px] font-bold text-[#FFFFFF]'>{third.name}</span>
                          <span className='text-[14px] font-medium text-white'>
                            {third.score.total.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>

              {/* Bottom div - preserved layout */}
              <div className='relative flex min-h-0 flex-1 flex-col items-center justify-end'>
                {lowest?.rank === 10 ? (
                  // case: lowest is rank 10 → fill with scroll only
                  <ScrollArea className='h-full w-full'>
                    <div className='mt-[2%] flex flex-col items-center'>
                      {defaultLeaderboard.slice(3).map((entry) => (
                        <div key={entry.rank} className='mb-[4%] h-[10.29dvh] w-[91.9%]'>
                          <RankRow entry={entry} />
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                ) : (
                  // case: lowest > 10 → scroll + separator + fixed lowest row
                  <>
                    <ScrollArea className='h-[67.6%] w-full'>
                      <div className='mt-[2%] flex flex-col items-center'>
                        {defaultLeaderboard.slice(3, -1).map((entry) => (
                          <div key={entry.rank} className='mb-[4%] h-[10.29dvh] w-[91.9%]'>
                            <RankRow entry={entry} />
                          </div>
                        ))}
                      </div>
                    </ScrollArea>

                    <Separator className='mb-[4%] w-[91.9%] bg-[#535548]' />

                    <div className='relative flex h-[10.29dvh] w-[91.9%] flex-col justify-end'>
                      <div className='absolute left-[10%] top-0 z-10 aspect-square h-[75%] rounded-full'>
                        <AspectRatio ratio={1}>
                          <Avatar className='h-full w-full'>
                            <AvatarImage src={lowest?.avatar} alt={lowest?.name} />
                            <AvatarFallback>{(lowest?.name || 'U').charAt(0)}</AvatarFallback>
                          </Avatar>
                        </AspectRatio>
                        <div className='absolute -bottom-[17.5%] left-1/2 z-10 flex -translate-x-1/2 items-center justify-center'>
                          <span className='text-[16px] font-bold text-white'>{lowest?.rank}</span>
                        </div>
                      </div>

                      <div className='backdrop-blur-xs flex h-[85%] w-full flex-col justify-center rounded-3xl border border-white/30 bg-white/5 pl-[30%] text-white'>
                        <span className='text-[16px] font-bold text-[#FF5500]'>{lowest?.name}</span>
                        <span className='text-[14px] font-medium'>{lowest?.score.total.toLocaleString()} điểm</span>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <DrawerFooter className='flex h-[11%] items-center justify-center px-[5%] pb-[5%]'>
          <div className='flex w-full justify-center gap-[7.5%]'>
            <DrawerClose className='w-[37.5%]'>
              <AspectRatio ratio={137 / 48} className='relative w-full'>
                <MediumerOrangeButton className='absolute inset-0 h-full w-full' />
                <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                  Đóng
                </span>
              </AspectRatio>
            </DrawerClose>
            <div
              className='w-[37.5%]'
              onClick={() => {
                onClose()
                openShare(null, 'rank')
              }}
            >
              <AspectRatio ratio={137 / 48} className='relative w-full cursor-pointer'>
                <MediumGreenButton className='absolute inset-0 h-full w-full' />
                <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                  Chia sẻ
                </span>
              </AspectRatio>
            </div>
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default LeaderboardDrawer
