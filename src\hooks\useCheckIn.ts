// src/hooks/useCheckIn.ts
import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useGameStore } from '@/stores/useGameStore'

interface CheckInRequest {
  userId: string
  gameCampaignId: string
}

export interface CheckInResponse {
  success: boolean
  rewards: number
}

export function useCheckIn() {
  const gameCampaignId = useGameStore((s) => s.gameCampaignId)

  return useMutation<CheckInResponse, Error, CheckInRequest>({
    mutationFn: async (payload) => {
      const res = await axios.post(
        '/api/checkIn',
        { ...payload, gameCampaignId },
        { headers: { 'Content-Type': 'application/json' } }
      )
      return res.data
    }
  })
}
