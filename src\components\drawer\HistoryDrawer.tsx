'use client'

import { <PERSON>er, <PERSON>er<PERSON>ontent, Drawer<PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, Drawer<PERSON>lose, DrawerFooter } from '@/components/ui/drawer'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import Image from 'next/image'
import LargeOrangeButton from '@/assets/drawer/largeOrangeButton.svg'
import { useHistoryGameCampaign } from '@/hooks/useHistoryGameCampaign'
import type { HistoryEntry } from '@/hooks/useHistoryGameCampaign'
import { useGameStore } from '@/stores/useGameStore'
import { useEffect, useState } from 'react'

interface HistoryEntryData {
  variant: 'turns' | 'gifts'
  time: string
  description: string
  icon?: string
  image?: string
}

interface HistoryEntryProps {
  entry: HistoryEntryData
}

const HistoryEntryComponent: React.FC<HistoryEntryProps> = ({ entry }) => {
  const isTurns = entry.variant === 'turns'

  return (
    <div className='w-[91.9%]'>
      <AspectRatio ratio={409 / 144} className='relative'>
        {/* Bottom div */}
        <div className='backdrop-blur-xs absolute bottom-0 left-0 right-0 flex h-[90.5%] flex-col items-center rounded-3xl border border-white/30 bg-white/5 text-white'>
          {/* Top section */}
          <div className='flex h-[41.1%] w-[90.8%] items-center justify-between'>
            <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text pl-[17.5%] text-[14px] font-bold text-transparent'>
              {isTurns ? 'Nhận lượt chơi' : 'Nhận quà'}
            </span>
            <span className='text-[12px] font-medium text-white'>{entry.time}</span>
          </div>

          {/* Separator */}
          <Separator className='w-[90.8%] bg-[#535548]' />

          {/* Bottom section */}
          <div className='flex w-[90.8%] flex-1 items-center'>
            <p className='text-[14px] font-medium text-white'>{entry.description}</p>
          </div>
        </div>

        {/* Top-left image */}
        <div className='absolute left-[5%] top-0 w-[12.5%]'>
          <AspectRatio ratio={955 / 968}>
            <Image
              src={isTurns ? '/dialog/mooncake.png' : '/dialog/giftbox.png'}
              alt={isTurns ? 'Mooncake' : 'Giftbox'}
              fill
              quality={100}
              className='drop-shadow-[2.12px_1.41px_3.18px_rgba(0,0,0,0.19)]'
            />
          </AspectRatio>
        </div>
      </AspectRatio>
    </div>
  )
}

// Helper function to convert API response to HistoryEntryData
const convertHistoryEntry = (apiEntry: HistoryEntry): HistoryEntryData => {
  // If entry has 'level' field -> 'gifts' variant, otherwise -> 'turns' variant
  const variant = apiEntry.level !== undefined || apiEntry.navigateTo !== undefined ? 'gifts' : 'turns'

  // Format the date
  const date = new Date(apiEntry.createdAt)
  const time = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')} - ${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}`

  return {
    variant,
    time,
    description: apiEntry.title.vi, // Use Vietnamese title
    icon: apiEntry.icon,
    image: apiEntry.image
  }
}

interface HistoryDrawerProps {
  open: boolean
  onClose: () => void
}

const HistoryDrawer: React.FC<HistoryDrawerProps> = ({ open, onClose }) => {
  const { mutateAsync: fetchHistory } = useHistoryGameCampaign()
  const { gameCampaignId } = useGameStore()
  const [historyData, setHistoryData] = useState<HistoryEntryData[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Fetch history data when drawer opens
  useEffect(() => {
    if (open && gameCampaignId) {
      const loadHistory = async () => {
        setIsLoading(true)
        try {
          const response = await fetchHistory({
            userId: 'x169079fd73a4533d4fd5e79097122a9b', // TODO: Get from user context
            gameCampaignId
          })
          const convertedData = response.map(convertHistoryEntry)
          setHistoryData(convertedData)
        } catch (error) {
          console.error('Failed to fetch history:', error)
          setHistoryData([])
        } finally {
          setIsLoading(false)
        }
      }
      loadHistory()
    }
  }, [open, gameCampaignId, fetchHistory])
  return (
    <Drawer open={open} onOpenChange={onClose}>
      <DrawerContent className='flex h-[97.5dvh] flex-col overflow-hidden'>
        {/* Fixed header */}
        <DrawerHeader className='h-[18.5%]'>
          <DrawerTitle className='font-bdstreet text-border-orange text-[32px]'>Lịch sử</DrawerTitle>
        </DrawerHeader>

        {/* Scrollable content */}
        <div className='flex-1 overflow-hidden'>
          {isLoading ? (
            <div className='flex h-full items-center justify-center'>
              <p className='text-[14px] font-medium text-white'>Đang tải...</p>
            </div>
          ) : historyData.length === 0 ? (
            <div className='flex h-full items-center justify-center'>
              <p className='text-[14px] font-medium text-white'>Không có lịch sử</p>
            </div>
          ) : (
            <ScrollArea className='h-full w-full'>
              <div className='flex flex-col items-center space-y-4 py-4'>
                {historyData.map((entry, i) => (
                  <HistoryEntryComponent key={i} entry={entry} />
                ))}
              </div>
            </ScrollArea>
          )}
        </div>

        {/* Footer */}
        <DrawerFooter className='flex h-[11%] items-center justify-center pb-[5%]'>
          <DrawerClose className='w-full'>
            <AspectRatio ratio={326 / 48} className='relative mx-auto w-[90%]'>
              <LargeOrangeButton className='absolute inset-0 h-full w-full' />
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                Đóng
              </span>
            </AspectRatio>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default HistoryDrawer
