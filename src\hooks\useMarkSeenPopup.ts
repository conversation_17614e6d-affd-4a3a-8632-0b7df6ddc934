import { useMutation } from '@tanstack/react-query'
import axios from 'axios'

interface MarkSeenPopupRequest {
  userId: string
  gameCampaignId: string
  missionGameId: string[]
}

export function useMarkSeenPopup() {
  return useMutation({
    mutationFn: async (payload: MarkSeenPopupRequest) => {
      const { data } = await axios.post('/api/markSeenPopup', payload)
      return data
    }
  })
}
