import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { AlertPopup } from '@/stores/useAlertPopupStore'

interface Payload {
  userId: string
  gameCampaignId: string
}

export function useAlertPopup() {
  return useMutation<AlertPopup[], Error, Payload>({
    mutationFn: async (payload: Payload) => {
      const { data } = await axios.post('/api/getAlertPopup', payload)
      return data
    }
  })
}
