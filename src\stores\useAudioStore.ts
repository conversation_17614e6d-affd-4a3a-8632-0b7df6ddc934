// src/stores/useAudioStore.ts
'use client'

import { create } from 'zustand'

interface AudioFiles {
  touch: string
  collectAward: string
  win: string
  lose: string
  backgroundMusic: string
}

const AUDIO_FILES: AudioFiles = {
  touch: '/audio/NhacNen_FullTrack.wav',
  collectAward: '/audio/Game Fail.wav',
  win: '/audio/Game Win.mp3',
  lose: '/audio/Game Fail.wav',
  backgroundMusic: '/audio/NhacNen_FullTrack.wav'
}

interface AudioState {
  isMuted: boolean
  isBackgroundMusicPlaying: boolean
  hasUserInteracted: boolean
  init: () => void
  playTouchSound: () => void
  playCollectAwardSound: () => void
  playWinSound: () => void
  playLoseSound: () => void
  startBackgroundMusic: () => void
  stopBackgroundMusic: () => void
  pauseBackgroundMusic: () => void
  resumeBackgroundMusic: () => void
  toggleMute: () => void
  ensureMusicContinuity: () => void
}

export const useAudioStore = create<AudioState>((set, get) => {
  // Refs stored outside state to avoid re-renders
  const audioRefs: Record<string, HTMLAudioElement> = {}
  let backgroundMusicRef: HTMLAudioElement | null = null

  const playSound = (soundType: keyof Omit<AudioFiles, 'backgroundMusic'>) => {
    if (get().isMuted) return
    const audio = audioRefs[soundType]
    if (audio) {
      audio.currentTime = 0
      audio.play().catch((err) => console.warn(`Failed to play ${soundType}`, err))
    }
  }

  return {
    isMuted: false,
    isBackgroundMusicPlaying: false,
    hasUserInteracted: true, // default true so autoplay works

    init: () => {
      // init sound effects
      Object.entries(AUDIO_FILES).forEach(([key, value]) => {
        if (key !== 'backgroundMusic') {
          const audio = new Audio(value as string)
          audio.preload = 'auto'
          audio.volume = 0.9
          audioRefs[key] = audio
        }
      })

      // init bgm
      try {
        const audio = new Audio(AUDIO_FILES.backgroundMusic)
        audio.preload = 'auto'
        audio.volume = 0.9
        audio.loop = true
        backgroundMusicRef = audio
      } catch (error) {
        console.warn(`Failed to load background music: ${AUDIO_FILES.backgroundMusic}`, error)
      }

      // listen once for interaction
      const handle = () => {
        set({ hasUserInteracted: true })
        document.removeEventListener('click', handle)
        document.removeEventListener('touchstart', handle)
        document.removeEventListener('keydown', handle)
      }
      document.addEventListener('click', handle)
      document.addEventListener('touchstart', handle)
      document.addEventListener('keydown', handle)
    },

    playTouchSound: () => playSound('touch'),
    playCollectAwardSound: () => playSound('collectAward'),
    playWinSound: () => playSound('win'),
    playLoseSound: () => playSound('lose'),

    startBackgroundMusic: () => {
      console.log('startBackgroundMusic called', {
        hasRef: !!backgroundMusicRef,
        isPlaying: get().isBackgroundMusicPlaying,
        isMuted: get().isMuted
      })

      if (!backgroundMusicRef) {
        console.warn('Background music ref not initialized')
        return
      }

      if (get().isBackgroundMusicPlaying) {
        console.log('Background music already playing')
        return
      }

      // Set volume based on current mute state
      backgroundMusicRef.volume = get().isMuted ? 0 : 0.9

      backgroundMusicRef
        .play()
        .then(() => {
          console.log('Background music started successfully')
          set({ isBackgroundMusicPlaying: true })
        })
        .catch((e) => console.warn('BGM play failed', e))
    },

    stopBackgroundMusic: () => {
      console.log('stopBackgroundMusic called')
      if (backgroundMusicRef && get().isBackgroundMusicPlaying) {
        backgroundMusicRef.pause()
        backgroundMusicRef.currentTime = 0 // Reset to beginning
        set({ isBackgroundMusicPlaying: false })
        console.log('Background music stopped')
      }
    },

    pauseBackgroundMusic: () => {
      console.log('pauseBackgroundMusic called')
      if (backgroundMusicRef && !backgroundMusicRef.paused) {
        backgroundMusicRef.pause()
        console.log('Background music paused')
      }
    },

    resumeBackgroundMusic: () => {
      console.log('resumeBackgroundMusic called', {
        hasRef: !!backgroundMusicRef,
        isPaused: backgroundMusicRef?.paused,
        isPlaying: get().isBackgroundMusicPlaying
      })

      if (backgroundMusicRef && backgroundMusicRef.paused && get().isBackgroundMusicPlaying) {
        // Set volume based on current mute state
        backgroundMusicRef.volume = get().isMuted ? 0 : 0.9
        backgroundMusicRef.play().catch((e) => console.warn('BGM resume failed', e))
        console.log('Background music resumed')
      }
    },

    toggleMute: () => {
      set((state) => {
        const newMuted = !state.isMuted
        if (newMuted) {
          // Mute all sound effects
          Object.values(audioRefs).forEach((a) => (a.volume = 0))
          // Pause background music and call native pauseBackgroundMusic
          if (backgroundMusicRef && state.isBackgroundMusicPlaying) {
            backgroundMusicRef.pause()
            set({ isBackgroundMusicPlaying: false })
          }
          // Call native pause function if available
          if (window.pauseBackgroundMusic) {
            window.pauseBackgroundMusic()
          }
        } else {
          // Unmute all sound effects
          Object.values(audioRefs).forEach((a) => (a.volume = 0.7))
          // Resume background music and call native resumeBackgroundMusic
          if (backgroundMusicRef && !state.isBackgroundMusicPlaying) {
            backgroundMusicRef.volume = 0.3
            backgroundMusicRef
              .play()
              .then(() => set({ isBackgroundMusicPlaying: true }))
              .catch((e) => console.warn('BGM resume failed', e))
          }
          // Call native resume function if available
          if (window.resumeBackgroundMusic) {
            window.resumeBackgroundMusic()
          }
        }
        return { isMuted: newMuted }
      })
    },

    ensureMusicContinuity: () => {
      console.log('ensureMusicContinuity called', {
        hasRef: !!backgroundMusicRef,
        isPlaying: get().isBackgroundMusicPlaying,
        isPaused: backgroundMusicRef?.paused,
        isMuted: get().isMuted
      })

      // If music should be playing but is paused, resume it
      if (backgroundMusicRef && get().isBackgroundMusicPlaying && backgroundMusicRef.paused) {
        backgroundMusicRef.volume = get().isMuted ? 0 : 0.9
        backgroundMusicRef.play().catch((e) => console.warn('Music continuity resume failed', e))
        console.log('Music continuity restored')
      }
    }
  }
})
