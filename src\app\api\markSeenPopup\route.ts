// src/app/api/markSeenPopup/route.ts
import { NextResponse } from 'next/server'
import axios from 'axios'

export async function POST(req: Request) {
  try {
    const body = await req.json()
    console.log('Incoming Request Body:', body)

    const baseUrl = process.env.BE_URL
    const accessKey = process.env.ACCESS_KEY_BE

    if (!baseUrl || !accessKey) {
      throw new Error('Missing BE_URL or ACCESS_KEY_BE in environment variables')
    }

    const { data } = await axios.post(`${baseUrl}/api-asker-vn/mark-seen-popup`, body, {
      headers: {
        accessKey,
        Authorization:
          'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTE0MTM4OH0.U19XYfTHpIO6vU_m8gYKGV4DAT8WVd9c2H5gG4D5Y6U',
        'Content-Type': 'application/json'
      }
    })

    console.log('Outgoing Response Data:', data)
    return NextResponse.json(data)
  } catch (err: unknown) {
    if (axios.isAxiosError(err)) {
      console.error('Axios error:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      })
      return NextResponse.json({ error: err.message }, { status: err.response?.status ?? 500 })
    }

    console.error('Unknown error:', err)
    return NextResponse.json({ error: 'Request failed' }, { status: 500 })
  }
}
