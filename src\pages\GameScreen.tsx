import Image from 'next/image'
import RoundIconButton from '@/components/common/RoundIconButton'
import BackIcon from '@/assets/game/back.svg'
import Mute from '@/assets/home/<USER>'
import Unmute from '@/assets/home/<USER>'
import { useAudioStore } from '@/stores/useAudioStore'
import PikachuGame from '@/components/gameScreen/gameBoard'
import { Progress } from '@/components/ui/progress'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import Countdown from 'react-countdown'
import { useState, useEffect } from 'react'
import { useGameStore } from '@/hooks/useGameBoard'
import { useGameInteractions } from '@/hooks/useGameBoard'
import TimeUpDialog from '@/components/dialog/TimeUpDialog'
import WinDialog from '@/components/dialog/WinDialog'
import { useShareDialogStore } from '@/stores/useShareDialogStore'
import GiveUpDialog from '@/components/dialog/GiveUpDialog'

interface GameScreenProps {
  stage: number
  onBack: () => void
  onNavigateToStage?: (stage: number) => void
}

const getStageTime = (stage: number) => {
  if (stage >= 1 && stage <= 5) return 180
  if (stage >= 6 && stage <= 10) return 150
  if (stage >= 11 && stage <= 15) return 120
  if (stage >= 16 && stage <= 20) return 100
  return 180
}

const GameScreen: React.FC<GameScreenProps> = ({ stage, onBack, onNavigateToStage }) => {
  const [showGiveUpDialog, setShowGiveUpDialog] = useState(false)

  const TOTAL_TIME = getStageTime(stage)
  const [progress, setProgress] = useState(100)
  const [restartKey, setRestartKey] = useState(0)
  const { isMuted: muted, toggleMute, ensureMusicContinuity } = useAudioStore()
  const [endTime, setEndTime] = useState(Date.now() + TOTAL_TIME * 1000)
  const [showWinDialog, setShowWinDialog] = useState(false)
  const [secondsLeft, setSecondsLeft] = useState(TOTAL_TIME)
  const { gameState, startNewGame, updateGameState } = useGameStore()
  const { handleUseHint } = useGameInteractions(gameState, updateGameState, stage)

  // Ensure background music continues when entering GameScreen
  useEffect(() => {
    ensureMusicContinuity()
  }, [ensureMusicContinuity])

  useEffect(() => {
    if (gameState.isWon && !showWinDialog) {
      setShowWinDialog(true)
    }
  }, [gameState.isWon, showWinDialog])

  const handleTick = ({ total }: { total: number }) => {
    const secs = Math.max(0, Math.ceil(total / 1000))
    setSecondsLeft(secs)
    setProgress((secs / TOTAL_TIME) * 100)
  }

  const searchSrc = gameState.hintCount > 0 ? '/game/search.png' : '/game/searchDisabled.png'
  const [timeUp, setTimeUp] = useState(false)
  const shouldStopTimer = gameState.isWon || timeUp || showGiveUpDialog

  const { open } = useShareDialogStore()

  const handleWinDialogNavigate = (nextStage: number) => {
    onNavigateToStage?.(nextStage + 1)
  }

  useEffect(() => {
    startNewGame(stage)
    setEndTime(Date.now() + TOTAL_TIME * 1000)
    setRestartKey((k) => k + 1)
    setProgress(100)
    setSecondsLeft(TOTAL_TIME)
    setTimeUp(false)
    setShowWinDialog(false)
  }, [stage, TOTAL_TIME, startNewGame])

  return (
    <div className='relative flex h-[100dvh] w-[100dvw] flex-col overflow-hidden bg-black'>
      <Image src='/game/gameBG.webp' alt='Background' fill quality={100} className='z-0' />
      <div className='relative z-10 flex h-full flex-col'>
        {/* Header */}
        <div className='flex h-[12.2%] items-end justify-between px-[2%]'>
          <RoundIconButton
            icon={<BackIcon className='z-20 h-[50%] w-[50%] -translate-x-[5%]' />}
            onClick={() => setShowGiveUpDialog(true)}
          />
          <p className='font-signwriter text-stroke text-[28px] text-[#FF5500] drop-shadow-[0_1.5px_0_#0A650E]'>
            Màn {stage}
          </p>
          <RoundIconButton
            icon={muted ? <Mute className='z-20 h-[50%] w-[50%]' /> : <Unmute className='z-20 h-[50%] w-[50%]' />}
            onClick={toggleMute}
          />
        </div>
        {/* Main */}
        <div className='flex h-[75.2%] w-full flex-1 items-center justify-center'>
          <PikachuGame stage={stage} />
        </div>
        {/* Footer with timer */}
        <div className='relative flex h-[12.6%] items-center'>
          <div className='relative h-auto w-[90%] translate-x-[5%]'>
            <Image
              src='/game/timerBar.png'
              alt='Timer bar'
              width={372}
              height={155}
              className='h-auto w-full -translate-x-[2.5%] object-cover'
              quality={100}
            />
            {/* Progress bar */}
            <div className='absolute inset-0 top-[12.75%] left-[12.35%] flex w-[65.75%] items-center'>
              <Progress value={progress} className='h-[10%]' />
            </div>
            {/* Clock image */}
            <div className='absolute top-[37%] left-[2.5%] z-20 w-[16%]'>
              <AspectRatio ratio={236 / 234}>
                <Image src='/game/clock.png' alt='Clock' fill quality={100} />
              </AspectRatio>
            </div>
            {/* Countdown text */}
            <div className='font-bdstreet absolute top-[37%] right-[20%] flex h-[12.5%] w-[7%] items-center justify-center text-[13px] font-bold text-[#FF8228]'>
              {shouldStopTimer ? (
                <span>{secondsLeft}</span>
              ) : (
                <Countdown
                  key={restartKey}
                  date={endTime}
                  onTick={handleTick}
                  onComplete={() => {
                    setProgress(0)
                    setTimeUp(true)
                  }}
                  renderer={() => <span>{secondsLeft}</span>}
                />
              )}
            </div>

            <div className='absolute top-[53.5%] left-[18%] z-20 h-[3%] w-[50%] rounded-full bg-white/50' />
          </div>
          <div className='absolute top-[20%] right-[3%] z-20 w-[16%]' onClick={handleUseHint}>
            <AspectRatio ratio={274 / 288}>
              <div className='relative h-full w-full'>
                <Image src={searchSrc} alt='Search' fill quality={100} className='object-contain' />
                <span className='font-bdstreet text-shadow-orange-btn absolute right-[27%] bottom-[22%] text-[10px] font-bold text-white'>
                  x{gameState.hintCount}
                </span>
              </div>
            </AspectRatio>
          </div>
        </div>
      </div>

      <TimeUpDialog
        open={timeUp}
        stage={stage}
        onRestart={() => {
          setTimeUp(false)
          startNewGame(stage)
          setEndTime(Date.now() + TOTAL_TIME * 1000)
          setRestartKey((k) => k + 1)
          setProgress(100)
        }}
        onQuit={onBack}
      />

      <WinDialog
        open={showWinDialog}
        onClose={onBack}
        stage={stage}
        variant='game'
        timeCompleted={secondsLeft}
        onNavigate={handleWinDialogNavigate}
        onShare={(stage) => {
          setShowWinDialog(false)
          onBack()
          open(stage)
        }}
      />

      <GiveUpDialog
        open={showGiveUpDialog}
        stage={stage}
        onClose={() => setShowGiveUpDialog(false)}
        onGiveUp={() => {
          setShowGiveUpDialog(false)
          onBack()
        }}
        onContinue={() => {
          setShowGiveUpDialog(false)
          setEndTime(Date.now() + secondsLeft * 1000) // resume from frozen time
          setRestartKey((k) => k + 1) // force Countdown to rerender
        }}
      />
    </div>
  )
}

export default GameScreen
